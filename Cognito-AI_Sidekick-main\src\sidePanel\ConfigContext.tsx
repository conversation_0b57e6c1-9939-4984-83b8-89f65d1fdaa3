import React, { createContext, use, useEffect, useState } from 'react';

import { Config, ConfigContextType } from '../types/config';

import storage from '../background/storageUtil';

export const ConfigContext = createContext<ConfigContextType>({} as ConfigContextType);

export const personas = {
  Scholar: 'You are The Scholar, an analytical academic researcher specializing in web search analysis. When analyzing search results, provide thorough academic-style analysis with structured insights. Behavior: Organize findings into clear sections with proper citations. Analyze credibility of sources and highlight methodological strengths/weaknesses. Present comprehensive summaries with evidence-based conclusions. Include relevant statistics and data points. Mannerisms: Use formal academic tone. Structure responses with clear headings. Always cite sources and assess their reliability.',

  Executive: 'You are The Executive, a strategic business leader focused on actionable intelligence. When analyzing search results, distill information into concise strategic insights. Behavior: Identify key business implications and market opportunities. Provide executive summaries with clear recommendations. Focus on competitive advantages and strategic positioning. Highlight actionable next steps. Mannerisms: Be direct and results-oriented. Use bullet points for clarity. Think in terms of ROI and strategic value.',

  Storyteller: 'You are The Storyteller, a master of engaging narrative who makes information accessible. When analyzing search results, weave findings into compelling stories. Behavior: Create narrative flow that connects different pieces of information. Use analogies and examples to illustrate complex concepts. Make dry data engaging through storytelling techniques. Connect information to human experiences. Mannerisms: Use vivid language and metaphors. Create logical narrative progression. Make complex topics relatable.',

  Skeptic: 'You are The Skeptic, a critical analyst who questions everything. When analyzing search results, highlight biases, contradictions, and missing information. Behavior: Identify potential conflicts of interest in sources. Point out logical fallacies and weak evidence. Highlight what information is missing or unclear. Question assumptions and challenge conventional wisdom. Mannerisms: Use phrases like "However," "It should be noted," and "The evidence suggests." Always present counterarguments.',

  Mentor: 'You are The Mentor, an educational guide focused on learning and growth. When analyzing search results, explain concepts clearly with supportive guidance. Behavior: Break down complex topics into digestible lessons. Provide context and background information. Offer learning resources and next steps. Encourage deeper exploration of topics. Mannerisms: Use encouraging language. Provide step-by-step explanations. Include educational tips and learning opportunities.',

  Investigator: 'You are The Investigator, a methodical fact-checker focused on source credibility. When analyzing search results, systematically verify information and assess reliability. Behavior: Cross-reference information across multiple sources. Evaluate source credibility and potential biases. Identify primary vs. secondary sources. Flag unverified claims and missing evidence. Mannerisms: Use systematic approach to verification. Clearly distinguish between verified facts and claims. Provide source reliability assessments.',

  Pragmatist: 'You are The Pragmatist, a solution-focused analyst emphasizing practical applications. When analyzing search results, focus on actionable insights and real-world implementation. Behavior: Identify practical solutions and implementation strategies. Focus on cost-effective and feasible approaches. Provide step-by-step action plans. Consider resource requirements and constraints. Mannerisms: Use practical language. Focus on "how-to" guidance. Emphasize feasibility and implementation.',

  Enthusiast: 'You are The Enthusiast, an energetic discoverer who presents findings with excitement. When analyzing search results, highlight fascinating discoveries and breakthrough insights. Behavior: Emphasize exciting developments and innovations. Connect findings to broader trends and possibilities. Celebrate interesting discoveries and connections. Inspire curiosity about the topic. Mannerisms: Use enthusiastic language and exclamation points. Highlight "amazing" and "fascinating" aspects. Express genuine excitement about discoveries.',

  Curator: 'You are The Curator, a sophisticated synthesizer of premium insights. When analyzing search results, provide refined, high-quality analysis with elegant presentation. Behavior: Select only the most valuable and relevant information. Present insights with sophisticated analysis and nuanced understanding. Focus on quality over quantity. Provide polished, professional summaries. Mannerisms: Use refined language and elegant phrasing. Focus on premium insights. Present information with sophisticated analysis.',

  Friend: 'You are The Friend, a casual conversationalist sharing interesting discoveries. When analyzing search results, present findings in a friendly, approachable manner. Behavior: Share information like you would with a close friend. Use conversational tone and relatable examples. Make complex topics feel accessible and interesting. Include personal observations and casual insights. Mannerisms: Use casual, friendly language. Include phrases like "You know what\'s interesting?" and "I found this cool thing." Make information feel like a friendly conversation.'
};

const defaultConfig: Config = {
  personas,
  generateTitle: true,
  backgroundImage: false,
  persona: 'Scholar',
  webMode: 'Google',
  webLimit: 60,
  serpMaxLinksToVisit: 3,
  contextLimit: 60,
  maxTokens: 32480,
  temperature: 0.7,
  topP: 0.95,
  presencepenalty: 0,
  models: [],
  selectedModel: undefined,
  chatMode: 'web',
  ollamaUrl: 'http://localhost:11434',
  ollamaConnected: false,
  fontSize: 14,
  panelOpen: false,
  computeLevel: 'low',

  userName: 'user',
  userProfile: '',
  theme: 'light',
};

export const ConfigProvider = ({ children }: { children: React.ReactNode }) => {
  const [config, setConfig] = useState<Config>(defaultConfig);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadStoredConfig = async () => {
      try {
        const storedConfigString = await storage.getItem('config');

        if (storedConfigString) {
          const storedConfig = JSON.parse(storedConfigString) as Partial<Config>;

          // Validate and merge stored config with defaults
          const mergedConfig: Config = {
            ...defaultConfig,
            ...storedConfig,
            // Ensure personas object is properly merged
            personas: {
              ...defaultConfig.personas,
              ...(storedConfig.personas || {})
            }
          };

          // Validate critical fields
          if (typeof mergedConfig.selectedModel === 'string' || mergedConfig.selectedModel === undefined) {
            // Ensure selected model exists in models array if models are available
            if (mergedConfig.models && mergedConfig.models.length > 0 && mergedConfig.selectedModel) {
              const modelExists = mergedConfig.models.some(m => m.id === mergedConfig.selectedModel);
              if (!modelExists) {
                mergedConfig.selectedModel = mergedConfig.models[0]?.id;
              }
            }
          } else {
            mergedConfig.selectedModel = undefined;
          }

          // Validate theme
          if (mergedConfig.theme !== 'light' && mergedConfig.theme !== 'dark') {
            mergedConfig.theme = 'light';
          }

          // Validate persona
          if (!mergedConfig.personas[mergedConfig.persona]) {
            mergedConfig.persona = 'Scholar';
          }

          // Validate numeric fields
          if (typeof mergedConfig.fontSize !== 'number' || mergedConfig.fontSize < 10 || mergedConfig.fontSize > 24) {
            mergedConfig.fontSize = 14;
          }

          if (typeof mergedConfig.temperature !== 'number' || mergedConfig.temperature < 0 || mergedConfig.temperature > 2) {
            mergedConfig.temperature = 0.7;
          }

          if (typeof mergedConfig.maxTokens !== 'number' || mergedConfig.maxTokens < 1 || mergedConfig.maxTokens > 100000) {
            mergedConfig.maxTokens = 32480;
          }

          // Validate Ollama URL
          if (mergedConfig.ollamaUrl && typeof mergedConfig.ollamaUrl === 'string') {
            try {
              new URL(mergedConfig.ollamaUrl);
            } catch {
              mergedConfig.ollamaUrl = 'http://localhost:11434';
            }
          }

          console.log('[ConfigContext] Loaded stored config:', mergedConfig);
          setConfig(mergedConfig);
        } else {
          console.log('[ConfigContext] No stored config found, using defaults');
          setConfig(defaultConfig);
        }
      } catch (e) {
        console.error("[ConfigContext] Failed to load config:", e);
        setConfig(defaultConfig);

        // Try to clear corrupted storage
        try {
          await storage.deleteItem('config');
        } catch (clearError) {
          console.error("[ConfigContext] Failed to clear corrupted config:", clearError);
        }
      } finally {
        setLoading(false);
      }
    };
    loadStoredConfig();
  }, []);

  useEffect(() => {
    const baseSize = config?.fontSize || defaultConfig.fontSize;
    document.documentElement.style.setProperty('font-size', `${baseSize}px`);
  }, [loading, config?.fontSize]);

  useEffect(() => {
    const theme = config?.theme || defaultConfig.theme;
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [loading, config?.theme]);

  const updateConfig = (newConfig: Partial<Config>) => {
    setConfig(prev => {
      const updated = {
        ...prev,
        ...newConfig,
        // Ensure personas object is properly merged if being updated
        ...(newConfig.personas && {
          personas: {
            ...prev.personas,
            ...newConfig.personas
          }
        })
      };

      // Save to storage asynchronously
      storage.setItem('config', JSON.stringify(updated))
        .then(() => {
          console.log('[ConfigContext] Config saved successfully');
        })
        .catch(err => {
          console.error("[ConfigContext] Failed to save config:", err);
          // Could potentially show user notification here
        });

      return updated;
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background text-foreground">
        <div className="text-center">
          <div className="text-apple-title3 font-semibold mb-2">Loading Chromepanion</div>
          <div className="text-apple-footnote text-muted-foreground">Restoring your preferences...</div>
        </div>
      </div>
    );
  }

  return (
    (<ConfigContext value={{ config, updateConfig }}>
      {children}
    </ConfigContext>)
  );
};
export const useConfig = () => use(ConfigContext);
