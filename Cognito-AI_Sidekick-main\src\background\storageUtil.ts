interface Storage {
  getItem: (key: string) => Promise<string | null>;
  setItem: (key: string, value: unknown) => Promise<void>;
  deleteItem: (key: string) => Promise<void>;
  clear: () => Promise<void>;
  isAvailable: () => boolean;
}

const storage: Storage = {
  getItem: async (key: string) => {
    try {
      if (!chrome?.storage?.local) {
        console.warn('[Storage] Chrome storage API not available');
        return null;
      }

      const data = await chrome.storage.local.get(key);
      const value = data[key];

      if (value === undefined || value === null) {
        return null;
      }

      return typeof value === 'string' ? value : JSON.stringify(value);
    } catch (e) {
      console.error(`[Storage] Failed to get item "${key}":`, e);
      return null;
    }
  },

  setItem: async (key: string, value: unknown) => {
    try {
      if (!chrome?.storage?.local) {
        throw new Error('Chrome storage API not available');
      }

      const serializableValue = typeof value === 'string' ? value : JSON.stringify(value);

      // Check storage quota before setting
      const bytesInUse = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES || 5242880; // 5MB default

      if (bytesInUse > quota * 0.9) { // Warn at 90% usage
        console.warn(`[Storage] Storage usage high: ${bytesInUse}/${quota} bytes`);
      }

      await chrome.storage.local.set({ [key]: serializableValue });
    } catch (e) {
      console.error(`[Storage] Failed to set item "${key}":`, e);
      throw e;
    }
  },

  deleteItem: async (key: string) => {
    try {
      if (!chrome?.storage?.local) {
        console.warn('[Storage] Chrome storage API not available');
        return;
      }

      await chrome.storage.local.remove(key);
    } catch (e) {
      console.error(`[Storage] Failed to delete item "${key}":`, e);
      throw e;
    }
  },

  clear: async () => {
    try {
      if (!chrome?.storage?.local) {
        console.warn('[Storage] Chrome storage API not available');
        return;
      }

      await chrome.storage.local.clear();
    } catch (e) {
      console.error('[Storage] Failed to clear storage:', e);
      throw e;
    }
  },

  isAvailable: () => {
    return !!(chrome?.storage?.local);
  }
};

export default storage;
